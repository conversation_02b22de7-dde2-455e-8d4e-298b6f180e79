import { withContentCollections } from "@content-collections/next";
import type { NextConfig } from "next";
import nextIntlPlugin from "next-intl/plugin";

const withNextIntl = nextIntlPlugin("./modules/i18n/request.ts");

const nextConfig: NextConfig = {
	transpilePackages: ["@repo/api", "@repo/auth", "@repo/database"],
	images: {
		remotePatterns: [
			{
				// google profile images
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				// github profile images
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
		],
	},
	async redirects() {
		return [
			{
				source: "/app/settings",
				destination: "/app/settings/general",
				permanent: true,
			},
			{
				source: "/app/:organizationSlug/settings",
				destination: "/app/:organizationSlug/settings/general",
				permanent: true,
			},
			{
				source: "/app/admin",
				destination: "/app/admin/users",
				permanent: true,
			},
			// Redirect English users from German contact path to English contact path
			{
				source: "/en/kontakt",
				destination: "/en/contact",
				permanent: true,
			},
			// Redirect German users from English contact path to German contact path
			{
				source: "/de/contact",
				destination: "/de/kontakt",
				permanent: true,
			},
			// Redirect root paths based on default locale
			{
				source: "/kontakt",
				destination: "/contact",
				permanent: true,
				locale: false,
			},
		];
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
};

export default withContentCollections(withNextIntl(nextConfig));
