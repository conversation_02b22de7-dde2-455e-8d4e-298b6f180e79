import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import Image from "next/image";

export function Logo({
	withLabel = true,
	className,
}: {
	className?: string;
	withLabel?: boolean;
}) {
	const t = useTranslations("common");
	const appName = t("appName");

	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			<Image
				src="/images/logo/android-chrome-192x192.png"
				alt={`${appName} Logo`}
				width={40}
				height={40}
				className="size-10"
			/>
			{withLabel && (
				<span className="ml-3 hidden text-lg md:block">{appName}</span>
			)}
		</span>
	);
}
