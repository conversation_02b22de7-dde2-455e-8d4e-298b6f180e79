"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { BrainIcon, GlobeIcon, SparklesIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function FeaturesSection() {
	const t = useTranslations("aiFanficGenerator.features");

	const features = [
		{
			icon: BrainIcon,
			title: t("aiPowered.title"),
			description: t("aiPowered.description"),
		},
		{
			icon: GlobeIcon,
			title: t("allFandoms.title"),
			description: t("allFandoms.description"),
		},
		{
			icon: SparklesIcon,
			title: t("creative.title"),
			description: t("creative.description"),
		},
	];

	return (
		<section className="py-16 lg:py-24 bg-gray-50">
			<div className="container max-w-7xl">
				<div className="text-center mb-16">
					<h2 className="font-bold text-4xl lg:text-5xl mb-4">
						{t("title")}
					</h2>
					<p className="text-xl text-foreground/70 max-w-3xl mx-auto">
						{t("subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
					{features.map((feature, index) => (
						<Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
							<CardHeader className="text-center pb-4">
								<div className="mx-auto mb-4 p-3 rounded-full bg-primary/10 w-fit">
									<feature.icon className="h-8 w-8 text-primary" />
								</div>
								<CardTitle className="text-xl font-semibold">
									{feature.title}
								</CardTitle>
							</CardHeader>
							<CardContent className="text-center">
								<CardDescription className="text-base leading-relaxed">
									{feature.description}
								</CardDescription>
							</CardContent>
						</Card>
					))}
				</div>

				<div className="flex flex-col items-center mt-16">
					<p className="text-lg text-foreground/80 mb-6 text-center">
						{t("cta.text")}
					</p>
					<Button
						size="lg"
						variant="primary"
						onClick={() => {
							const formElement = document.getElementById("fanfic-generator-form");
							if (formElement) {
								formElement.scrollIntoView({ behavior: "smooth" });
							}
						}}
					>
						{t("cta.button")}
					</Button>
				</div>
			</div>
		</section>
	);
}
