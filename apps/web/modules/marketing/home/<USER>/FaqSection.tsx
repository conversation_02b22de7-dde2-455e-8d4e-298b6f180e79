import {
	Accordion,
	Accordion<PERSON><PERSON>nt,
	Accordion<PERSON><PERSON>,
	AccordionTrigger,
} from "@ui/components/accordion";
import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";

export function FaqSection({ className }: { className?: string }) {
	const t = useTranslations("aiFanficGenerator.faq");

	const items = [
		{
			question: t("questions.q1.question"),
			answer: t("questions.q1.answer"),
		},
		{
			question: t("questions.q2.question"),
			answer: t("questions.q2.answer"),
		},
		{
			question: t("questions.q6.question"),
			answer: t("questions.q6.answer"),
		},
		{
			question: t("questions.q7.question"),
			answer: t("questions.q7.answer"),
		},
		{
			question: t("questions.q8.question"),
			answer: t("questions.q8.answer"),
		},
		{
			question: t("questions.q9.question"),
			answer: t("questions.q9.answer"),
		},
		{
			question: t("questions.q10.question"),
			answer: t("questions.q10.answer"),
		},
		{
			question: t("questions.q11.question"),
			answer: t("questions.q11.answer"),
		},
	];

	if (!items) {
		return null;
	}

	return (
		<section
			className={cn("scroll-mt-20 border-t py-12 lg:py-16", className)}
			id="fragen"
		>
			<div className="container max-w-5xl">
				<div className="mb-12 lg:text-center">
					<h2 className="mb-2 font-bold text-4xl lg:text-5xl">
						{t("title")}
					</h2>
				</div>
				<div className="mx-auto max-w-4xl">
					<Accordion type="single" collapsible className="w-full space-y-4">
						{items.map((item, i) => (
							<AccordionItem
								key={`faq-item-${i}`}
								value={`item-${i}`}
								className="rounded-lg border bg-card px-6 py-4 shadow-sm"
							>
								<AccordionTrigger className="text-left hover:no-underline [&[data-state=open]>svg]:rotate-180">
									<h4 className="font-semibold text-lg pr-4">
										{item.question}
									</h4>
								</AccordionTrigger>
								<AccordionContent className="pt-4 pb-2">
									<p className="text-foreground/70 leading-relaxed">
										{item.answer}
									</p>
								</AccordionContent>
							</AccordionItem>
						))}
					</Accordion>
				</div>
			</div>
		</section>
	);
}
