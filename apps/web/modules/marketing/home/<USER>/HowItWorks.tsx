"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { 
	MessageSquareIcon, 
	SparklesIcon, 
	EditIcon, 
	ArrowRightIcon,
	CheckCircleIcon 
} from "lucide-react";
import { useTranslations } from "next-intl";

export function HowItWorks() {
	const t = useTranslations("aiFanficGenerator.howItWorks");

	const scrollToForm = () => {
		const formElement = document.getElementById("fanfic-generator-form");
		if (formElement) {
			formElement.scrollIntoView({ behavior: "smooth" });
		}
	};

	const steps = [
		{
			icon: MessageSquareIcon,
			title: t("steps.step1.title"),
			description: t("steps.step1.description"),
			color: "text-blue-500",
			bgColor: "bg-blue-50 dark:bg-blue-950/20",
		},
		{
			icon: SparklesIcon,
			title: t("steps.step2.title"),
			description: t("steps.step2.description"),
			color: "text-purple-500",
			bgColor: "bg-purple-50 dark:bg-purple-950/20",
		},
		{
			icon: EditIcon,
			title: t("steps.step3.title"),
			description: t("steps.step3.description"),
			color: "text-green-500",
			bgColor: "bg-green-50 dark:bg-green-950/20",
		},
	];

	return (
		<section id="anleitung" className="scroll-mt-16 py-16 lg:py-24">
			<div className="container max-w-6xl">
				<div className="mb-16 text-center">
					<h2 className="mb-4 font-bold text-4xl lg:text-5xl">
						{t("title")}
					</h2>
					<p className="mx-auto max-w-2xl text-lg text-muted-foreground">
						{t("subtitle")}
					</p>
				</div>

				<div className="grid gap-8 md:grid-cols-3 lg:gap-12">
					{steps.map((step, index) => (
						<div key={index} className="relative">
							{/* Connection Line */}
							{index < steps.length - 1 && (
								<div className="absolute top-16 left-1/2 z-0 hidden h-px w-full bg-gradient-to-r from-border to-transparent md:block lg:top-20" />
							)}
							
							{/* Step Card */}
							<div className="relative z-10 text-center">
								{/* Icon */}
								<div className={cn(
									"mx-auto mb-6 flex size-16 items-center justify-center rounded-full",
									step.bgColor
								)}>
									<step.icon className={cn("size-8", step.color)} />
								</div>

								{/* Step Number */}
								<div className="mb-4 flex items-center justify-center">
									<span className="flex size-8 items-center justify-center rounded-full bg-primary font-semibold text-primary-foreground text-sm">
										{index + 1}
									</span>
								</div>

								{/* Content */}
								<h3 className="mb-3 font-semibold text-xl">
									{step.title}
								</h3>
								<p className="text-muted-foreground">
									{step.description}
								</p>
							</div>
						</div>
					))}
				</div>

				{/* Features Grid */}
				<div className="mt-20 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
					<div className="rounded-lg border bg-card p-6 text-center">
						<CheckCircleIcon className="mx-auto mb-4 size-8 text-green-500" />
						<h4 className="mb-2 font-semibold">{t("features.quickEasy.title")}</h4>
						<p className="text-muted-foreground text-sm">
							{t("features.quickEasy.description")}
						</p>
					</div>
					<div className="rounded-lg border bg-card p-6 text-center">
						<CheckCircleIcon className="mx-auto mb-4 size-8 text-green-500" />
						<h4 className="mb-2 font-semibold">{t("features.personalDignified.title")}</h4>
						<p className="text-muted-foreground text-sm">
							{t("features.personalDignified.description")}
						</p>
					</div>
					<div className="rounded-lg border bg-card p-6 text-center md:col-span-2 lg:col-span-1">
						<CheckCircleIcon className="mx-auto mb-4 size-8 text-green-500" />
						<h4 className="mb-2 font-semibold">{t("features.freeAvailable.title")}</h4>
						<p className="text-muted-foreground text-sm">
							{t("features.freeAvailable.description")}
						</p>
					</div>
				</div>

				{/* CTA */}
				<div className="mt-16 flex justify-center">
					<Button size="lg" onClick={scrollToForm}>
						{t("cta")}
						<ArrowRightIcon className="ml-2 size-4" />
					</Button>
				</div>
			</div>
		</section>
	);
}
