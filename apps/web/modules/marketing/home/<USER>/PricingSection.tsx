"use client";
import { <PERSON><PERSON> } from "@ui/components/button";
import { CheckIcon, SparklesIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function PricingSection() {
	const t = useTranslations("pricing");

	const scrollToForm = () => {
		const formElement = document.getElementById("fanfic-generator-form");
		if (formElement) {
			formElement.scrollIntoView({ behavior: "smooth" });
		}
	};

	const features = [
		t("freePlan.features.unlimited"),
		t("freePlan.features.aiPowered"),
		t("freePlan.features.instant"),
		t("freePlan.features.editable"),
		t("freePlan.features.noCosts"),
		t("freePlan.features.privacy"),
	];

	return (
		<section id="preise" className="scroll-mt-16 py-12 lg:py-16">
			<div className="container max-w-5xl">
				<div className="mb-12 lg:text-center">
					<h2 className="font-bold text-4xl lg:text-5xl">
						{t("title")}
					</h2>
					<p className="mt-3 text-lg opacity-50">
						{t("description")}
					</p>
				</div>

				{/* MVP Free Plan */}
				<div className="mx-auto max-w-md">
					<div className="relative rounded-2xl border-2 border-primary bg-card p-8 shadow-lg">
						{/* Badge */}
						<div className="absolute -top-4 left-1/2 -translate-x-1/2">
							<div className="flex items-center gap-2 rounded-full bg-primary px-4 py-2 font-semibold text-primary-foreground text-sm">
								<SparklesIcon className="size-4" />
								{t("freePlan.badge")}
							</div>
						</div>

						{/* Header */}
						<div className="mb-8 text-center">
							<h3 className="mb-2 font-bold text-2xl">{t("freePlan.title")}</h3>
							<div className="mb-4">
								<span className="font-bold text-5xl">{t("freePlan.price")}</span>
								<span className="text-muted-foreground">{t("freePlan.period")}</span>
							</div>
							<p className="text-muted-foreground">
								{t("freePlan.description")}
							</p>
						</div>

						{/* Features */}
						<div className="mb-8 space-y-4">
							{features.map((feature, index) => (
								<div key={index} className="flex items-center gap-3">
									<CheckIcon className="size-5 text-green-500" />
									<span className="text-sm">{feature}</span>
								</div>
							))}
						</div>

						{/* CTA */}
						<Button
							size="lg"
							className="w-full"
							onClick={scrollToForm}
						>
							{t("freePlan.cta")}
						</Button>
					</div>
				</div>

				{/* Future Plans Notice */}
				<div className="mt-12 text-center">
					<p className="text-muted-foreground text-sm">
						{t("futureNotice")}
					</p>
				</div>
			</div>
		</section>
	);
}
