"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { ChevronDownIcon, ChevronUpIcon, LoaderIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface FormData {
	name: string;
	relationship: string;
	age: string;
	pronouns: string;
	personality: string;
	story: string;
	achievements: string;
	hobbies: string;
	sayings: string;
	secretStory: string;
	impact: string;
	family: string;
	missedMost: string;
	religion: string;
	additionalRequests: string;
}

export function TrauerredeForm() {
	const t = useTranslations("aiTrauerrede.form");
	const tResult = useTranslations("aiTrauerrede.result");
	const [formData, setFormData] = useState<FormData>({
		name: "",
		relationship: "",
		age: "",
		pronouns: "",
		personality: "",
		story: "",
		achievements: "",
		hobbies: "",
		sayings: "",
		secretStory: "",
		impact: "",
		family: "",
		missedMost: "",
		religion: "",
		additionalRequests: "",
	});
	const [showExtended, setShowExtended] = useState(false);
	const [isGenerating, setIsGenerating] = useState(false);
	const [generatedSpeech, setGeneratedSpeech] = useState<string | null>(null);

	const handleInputChange = (field: keyof FormData, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	// Calculate speech duration based on word count
	// Average speaking rate: 150-160 words per minute for German
	const calculateSpeechDuration = (text: string) => {
		const words = text.trim().split(/\s+/).length;
		const minutes = Math.round((words / 155) * 10) / 10; // 155 words per minute average
		return { words, minutes };
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsGenerating(true);

		try {
			const response = await fetch("/api/generate-trauerrede", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(formData),
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`;
				console.error("API Error:", errorMessage);
				throw new Error(errorMessage);
			}

			const data = await response.json();
			setGeneratedSpeech(data.speech);
		} catch (error) {
			console.error("Error generating speech:", error);
			// Show user-friendly error message
			const errorMessage = error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten";
			alert(`Fehler beim Erstellen der Trauerrede: ${errorMessage}`);
		} finally {
			setIsGenerating(false);
		}
	};

	const basicQuestions = [
		{ key: "name" as keyof FormData, type: "input", required: true },
		{ key: "relationship" as keyof FormData, type: "input", required: true },
		{ key: "age" as keyof FormData, type: "input", required: false },
		{ key: "pronouns" as keyof FormData, type: "input", required: false },
		{ key: "personality" as keyof FormData, type: "textarea", required: true },
		{ key: "story" as keyof FormData, type: "textarea", required: true },
	];

	const extendedQuestions = [
		{ key: "achievements" as keyof FormData, type: "textarea", required: false },
		{ key: "hobbies" as keyof FormData, type: "textarea", required: false },
		{ key: "sayings" as keyof FormData, type: "textarea", required: false },
		{ key: "secretStory" as keyof FormData, type: "textarea", required: false },
		{ key: "impact" as keyof FormData, type: "textarea", required: false },
		{ key: "family" as keyof FormData, type: "textarea", required: false },
		{ key: "missedMost" as keyof FormData, type: "textarea", required: false },
		{ key: "religion" as keyof FormData, type: "input", required: false },
		{ key: "additionalRequests" as keyof FormData, type: "textarea", required: false },
	];

	if (generatedSpeech) {
		const { words, minutes } = calculateSpeechDuration(generatedSpeech);

		return (
			<div className="mx-auto max-w-4xl">
				<div className="rounded-lg border bg-card p-6 shadow-sm">
					<h3 className="mb-4 font-semibold text-xl">
						{tResult("title")}
					</h3>
					<p className="mb-4 text-muted-foreground">
						{tResult("subtitle")}
					</p>

					{/* Speech Duration Info */}
					<div className="mb-4 flex flex-wrap gap-4 rounded-md bg-blue-50 p-4 dark:bg-blue-950/20">
						<div className="flex items-center gap-2">
							<svg className="size-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
							<span className="font-medium text-blue-800 text-sm dark:text-blue-200">
								{tResult("speechDuration")}: {minutes} {tResult("minutes")}
							</span>
						</div>
						<div className="flex items-center gap-2">
							<svg className="size-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
							</svg>
							<span className="text-blue-700 text-sm dark:text-blue-300">
								{words} {tResult("words")}
							</span>
						</div>
					</div>

					<div className="mb-6 rounded-md border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-950/20">
						<p className="text-amber-800 text-sm dark:text-amber-200">
							<strong>Wichtiger Hinweis:</strong> Bitte überprüfen Sie die generierte Trauerrede sorgfältig und passen Sie sie nach Ihren persönlichen Wünschen an. Die KI kann gelegentlich unpassende Inhalte generieren.
						</p>
					</div>
					<div className="mb-6 rounded-md bg-muted p-4">
						<div className="whitespace-pre-wrap text-sm leading-relaxed">
							{generatedSpeech}
						</div>
					</div>
					<div className="flex flex-wrap gap-3">
						<Button
							onClick={() => navigator.clipboard.writeText(generatedSpeech)}
							variant="outline"
						>
							{tResult("copy")}
						</Button>
						<Button
							onClick={() => setGeneratedSpeech(null)}
							variant="outline"
						>
							{tResult("edit")}
						</Button>
						<Button
							onClick={() => {
								setGeneratedSpeech(null);
								handleSubmit(new Event("submit") as any);
							}}
							variant="outline"
						>
							{tResult("regenerate")}
						</Button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="mx-auto max-w-4xl">
			<div className="mb-8 text-center">
				<h2 className="mb-4 font-bold text-3xl">{t("title")}</h2>
				<p className="text-lg text-muted-foreground">{t("subtitle")}</p>
			</div>

			<form onSubmit={handleSubmit} className="space-y-8">
				{/* Basic Questions */}
				<div className="rounded-lg border bg-card p-6 shadow-sm">
					<h3 className="mb-6 font-semibold text-xl">{t("basicQuestions")}</h3>
					<div className="grid gap-6 md:grid-cols-2">
						{basicQuestions.map(({ key, type, required }) => (
							<div key={key} className={cn(
								type === "textarea" && "md:col-span-2"
							)}>
								<label className="mb-2 block font-medium text-sm">
									{t(`questions.${key}.label`)}
									{required && <span className="text-red-500 ml-1">*</span>}
								</label>
								{type === "input" ? (
									<Input
										value={formData[key]}
										onChange={(e) => handleInputChange(key, e.target.value)}
										placeholder={t(`questions.${key}.placeholder`)}
										required={required}
									/>
								) : (
									<Textarea
										value={formData[key]}
										onChange={(e) => handleInputChange(key, e.target.value)}
										placeholder={t(`questions.${key}.placeholder`)}
										rows={3}
										required={required}
									/>
								)}
							</div>
						))}
					</div>
				</div>

				{/* Extended Questions */}
				<div className="rounded-lg border bg-card p-6 shadow-sm">
					<div className="mb-6 flex items-center justify-between">
						<h3 className="font-semibold text-xl">{t("extendedQuestions")}</h3>
						<Button
							type="button"
							variant="ghost"
							onClick={() => setShowExtended(!showExtended)}
							className="flex items-center gap-2"
						>
							{showExtended ? t("showLess") : t("showMore")}
							{showExtended ? (
								<ChevronUpIcon className="size-4" />
							) : (
								<ChevronDownIcon className="size-4" />
							)}
						</Button>
					</div>

					{showExtended && (
						<div className="grid gap-6">
							{extendedQuestions.map(({ key, type, required }) => (
								<div key={key}>
									<label className="mb-2 block font-medium text-sm">
										{t(`questions.${key}.label`)}
										{required && <span className="text-red-500 ml-1">*</span>}
									</label>
									{type === "input" ? (
										<Input
											value={formData[key]}
											onChange={(e) => handleInputChange(key, e.target.value)}
											placeholder={t(`questions.${key}.placeholder`)}
											required={required}
										/>
									) : (
										<Textarea
											value={formData[key]}
											onChange={(e) => handleInputChange(key, e.target.value)}
											placeholder={t(`questions.${key}.placeholder`)}
											rows={3}
											required={required}
										/>
									)}
								</div>
							))}
						</div>
					)}
				</div>

				{/* Submit Button */}
				<div className="text-center">
					<Button
						type="submit"
						size="lg"
						disabled={isGenerating || !formData.name || !formData.relationship || !formData.personality || !formData.story}
						className="min-w-48"
					>
						{isGenerating ? (
							<>
								<LoaderIcon className="mr-2 size-4 animate-spin" />
								{t("generating")}
							</>
						) : (
							t("generate")
						)}
					</Button>
				</div>
			</form>
		</div>
	);
}
