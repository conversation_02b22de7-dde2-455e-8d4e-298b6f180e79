"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { ArrowRightIcon, HeartIcon, SparklesIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";

export function Hero() {
	const t = useTranslations("aiFanficGenerator.hero");

	const scrollToForm = () => {
		const formElement = document.getElementById("fanfic-generator-form");
		if (formElement) {
			formElement.scrollIntoView({ behavior: "smooth" });
		}
	};

	return (
		<div className="relative max-w-full overflow-x-hidden bg-linear-to-b from-0% from-card to-[50vh] to-background">
			<div className="absolute left-1/2 z-10 ml-[-500px] h-[500px] w-[1000px] rounded-full bg-linear-to-r from-primary to-bg opacity-20 blur-[150px]" />
			<div className="container relative z-20 pt-44 pb-12 text-center lg:pb-16">
				<div className="mb-4 flex justify-center">
					<div className="mx-auto flex flex-wrap items-center justify-center rounded-full border border-highlight/30 p-px px-4 py-1 font-normal text-highlight text-sm shadow-sm">
						<span className="flex items-center gap-2 rounded-full font-semibold text-highlight">
							<SparklesIcon className="size-4" />
							{t("badge.new")}
						</span>
						<span className="ml-1 block font-medium text-foreground">
							{t("badge.freePhase")}
						</span>
					</div>
				</div>

				<h1 className="mx-auto max-w-4xl text-balance font-bold text-5xl lg:text-7xl">
					{t("title")}
				</h1>

				<p className="mx-auto mt-6 max-w-2xl text-balance text-foreground/60 text-xl">
					{t("description")}
				</p>

				<div className="mt-8 flex flex-col items-center justify-center gap-4 md:flex-row">
					<Button size="lg" variant="primary" onClick={scrollToForm}>
						{t("cta")}
						<ArrowRightIcon className="ml-2 size-4" />
					</Button>
					<Button variant="light" size="lg" asChild>
						<Link href="#anleitung">{t("ctaSecondary")}</Link>
					</Button>
				</div>

				{/* Features Section */}
				<div className="mt-20 px-8 text-center">
					<h5 className="font-semibold text-foreground/50 text-xs uppercase tracking-wider">
						{t("trustSection.title")}
					</h5>

					<div className="mt-8 flex flex-col items-center justify-center gap-8 text-foreground/50 md:flex-row md:gap-12">
						<div className="flex items-center gap-3">
							<SparklesIcon className="size-6 text-primary" />
							<span className="font-medium">{t("trustSection.aiPowered")}</span>
						</div>
						<div className="flex items-center gap-3">
							<HeartIcon className="size-6 text-primary" />
							<span className="font-medium">{t("trustSection.empathetic")}</span>
						</div>
						<div className="flex items-center gap-3">
							<svg className="size-6 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
								<path d="M9 12l2 2 4-4"/>
								<path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
							</svg>
							<span className="font-medium">{t("trustSection.securePrivate")}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
