"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { FileTextIcon, CopyIcon, CheckIcon, HashIcon, TypeIcon, AlignLeftIcon, ClockIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";

interface TextCountResult {
	characters: number;
	charactersNoSpaces: number;
	words: number;
	sentences: number;
	paragraphs: number;
	estimatedReadingTime: number; // in minutes
}

export function SpeechTimerTool() {
	const t = useTranslations("textCounter");
	const [text, setText] = useState("");
	const [readingSpeed, setReadingSpeed] = useState(200); // words per minute for reading
	const [result, setResult] = useState<TextCountResult | null>(null);
	const [copied, setCopied] = useState(false);

	// Auto-calculate when text changes (debounced)
	useEffect(() => {
		if (!text.trim()) {
			setResult(null);
			return;
		}

		const timeoutId = setTimeout(() => {
			calculateCounts();
		}, 300); // 300ms debounce

		return () => clearTimeout(timeoutId);
	}, [text, readingSpeed]);

	const calculateCounts = () => {
		if (!text.trim()) return;

		// Character count (with spaces)
		const characters = text.length;

		// Character count (without spaces)
		const charactersNoSpaces = text.replace(/\s/g, '').length;

		// Word count (split by whitespace and filter out empty strings)
		const words = text.trim().split(/\s+/).filter(word => word.length > 0);
		const wordCount = words.length;

		// Sentence count (split by sentence endings)
		const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;

		// Paragraph count (split by double line breaks or single line breaks)
		const paragraphs = text.split(/\n\s*\n|\n/).filter(paragraph => paragraph.trim().length > 0).length;

		// Estimated reading time (average reading speed is 200-250 WPM)
		const estimatedReadingTime = Math.ceil(wordCount / readingSpeed);

		setResult({
			characters,
			charactersNoSpaces,
			words: wordCount,
			sentences,
			paragraphs,
			estimatedReadingTime,
		});
	};

	const copyResults = async () => {
		if (!result) return;

		const resultText = `${t("results.characters")}: ${result.characters}
${t("results.charactersNoSpaces")}: ${result.charactersNoSpaces}
${t("results.words")}: ${result.words}
${t("results.sentences")}: ${result.sentences}
${t("results.paragraphs")}: ${result.paragraphs}
${t("results.readingTime")}: ${result.estimatedReadingTime} ${result.estimatedReadingTime === 1 ? t("results.minute") : t("results.minutes")}`;

		try {
			await navigator.clipboard.writeText(resultText);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error('Failed to copy: ', err);
		}
	};

	return (
		<div className="w-full max-w-4xl mx-auto space-y-8">
			{/* Input Form */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileTextIcon className="w-5 h-5" />
						{t("title")}
					</CardTitle>
					<CardDescription>
						{t("description")}
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Text Input */}
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<Label htmlFor="textInput">{t("form.textInput")}</Label>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setText("The quick brown fox jumps over the lazy dog. This is a sample text to demonstrate the text counting functionality. It contains multiple sentences and paragraphs.\n\nThis is the second paragraph. It helps show how the tool counts different elements in your text. You can paste any text here to get detailed statistics about it.")}
								className="text-xs"
							>
								{t("form.tryExample")}
							</Button>
						</div>
						<Textarea
							id="textInput"
							placeholder={t("form.textPlaceholder")}
							value={text}
							onChange={(e) => setText(e.target.value)}
							rows={8}
							className="min-h-[200px]"
						/>
						{text && (
							<p className="text-xs text-muted-foreground">
								{t("form.liveCount")}: {text.trim().split(/\s+/).filter(word => word.length > 0).length} {t("form.words")}
							</p>
						)}
					</div>

					{/* Reading Speed Input */}
					<div className="space-y-2">
						<Label htmlFor="readingSpeed" className="flex items-center gap-2">
							{t("form.readingSpeed")}
							<ClockIcon className="w-4 h-4 text-muted-foreground" />
						</Label>
						<Input
							id="readingSpeed"
							type="number"
							min="100"
							max="400"
							value={readingSpeed}
							onChange={(e) => setReadingSpeed(Number(e.target.value))}
							className="max-w-xs"
						/>
						<p className="text-sm text-muted-foreground">
							{t("form.readingSpeedHelp")}
						</p>
					</div>

					{/* Action Buttons */}
					<div className="flex gap-2">
						<Button
							onClick={calculateCounts}
							disabled={!text.trim()}
							size="lg"
							className="flex-1 sm:flex-none"
						>
							{t("form.analyze")}
						</Button>
						{result && (
							<Button
								variant="outline"
								size="lg"
								onClick={copyResults}
								className="flex items-center gap-2"
							>
								{copied ? (
									<CheckIcon className="w-4 h-4" />
								) : (
									<CopyIcon className="w-4 h-4" />
								)}
								{copied ? t("form.copied") : t("form.copy")}
							</Button>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Results */}
			{result && (
				<Card>
					<CardHeader>
						<CardTitle>{t("results.title")}</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<TypeIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.characters.toLocaleString()}
								</div>
								<div className="text-xs text-muted-foreground">
									{t("results.characters")}
								</div>
							</div>
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<HashIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.charactersNoSpaces.toLocaleString()}
								</div>
								<div className="text-xs text-muted-foreground">
									{t("results.charactersNoSpaces")}
								</div>
							</div>
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<FileTextIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.words.toLocaleString()}
								</div>
								<div className="text-xs text-muted-foreground">
									{t("results.words")}
								</div>
							</div>
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<AlignLeftIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.sentences.toLocaleString()}
								</div>
								<div className="text-xs text-muted-foreground">
									{t("results.sentences")}
								</div>
							</div>
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<AlignLeftIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.paragraphs.toLocaleString()}
								</div>
								<div className="text-xs text-muted-foreground">
									{t("results.paragraphs")}
								</div>
							</div>
							<div className="text-center p-4 bg-muted rounded-lg">
								<div className="flex items-center justify-center mb-2">
									<ClockIcon className="w-5 h-5 text-primary" />
								</div>
								<div className="text-2xl font-bold text-primary mb-1">
									{result.estimatedReadingTime}
								</div>
								<div className="text-xs text-muted-foreground">
									{result.estimatedReadingTime === 1 ? t("results.minute") : t("results.minutes")}
								</div>
							</div>
						</div>

						{/* Usage Tips */}
						<div className="mt-8">
							<h3 className="text-lg font-semibold mb-4">{t("results.tips.title")}</h3>
							<ul className="space-y-2 text-sm text-muted-foreground">
								<li className="flex items-start gap-2">
									<span className="text-primary mt-1">•</span>
									{t("results.tips.characters")}
								</li>
								<li className="flex items-start gap-2">
									<span className="text-primary mt-1">•</span>
									{t("results.tips.words")}
								</li>
								<li className="flex items-start gap-2">
									<span className="text-primary mt-1">•</span>
									{t("results.tips.readability")}
								</li>
								<li className="flex items-start gap-2">
									<span className="text-primary mt-1">•</span>
									{t("results.tips.structure")}
								</li>
							</ul>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
