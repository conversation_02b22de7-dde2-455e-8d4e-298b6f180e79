import { config } from "@repo/config";
import { getMessagesForLocale } from "@repo/i18n";
import type { MetadataRoute } from "next";
import { headers } from "next/headers";

export default async function manifest(): Promise<MetadataRoute.Manifest> {
	// Get the locale from the request headers or use default
	const headersList = await headers();
	const pathname = headersList.get("x-pathname") || "";
	const locale = pathname.startsWith("/de") ? "de" : config.i18n.defaultLocale;

	const messages = await getMessagesForLocale(locale);
	const appName = messages.common?.appName || config.appName;

	const descriptions = {
		en: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
		de: "Erstellen Sie fantastische Fanfiction-Geschichten mit unserem AI Fanfic Generator. Kostenloses Tool für alle Fandoms - schreiben Sie überzeugende Fan Fiction in Minuten mit KI-Unterstützung.",
	};

	const description = descriptions[locale as keyof typeof descriptions] || descriptions.en;

	return {
		name: appName,
		short_name: appName,
		description,
		start_url: "/",
		display: "standalone",
		background_color: "#ffffff",
		theme_color: "#ffffff",
		icons: [
			{
				src: "/images/logo/android-chrome-192x192.png",
				sizes: "192x192",
				type: "image/png",
			},
			{
				src: "/images/logo/android-chrome-512x512.png",
				sizes: "512x512",
				type: "image/png",
			},
			{
				src: "/images/logo/apple-touch-icon.png",
				sizes: "180x180",
				type: "image/png",
			},
		],
	};
}
