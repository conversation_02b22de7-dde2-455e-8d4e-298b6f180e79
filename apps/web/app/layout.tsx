import type { Metada<PERSON> } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";
import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";

export const metadata: Metadata = {
	title: {
		absolute: config.appName,
		default: config.appName,
		template: `%s | ${config.appName}`,
	},
	description: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
	keywords: ["AI Fanfic Generator", "Fanfiction Generator", "KI Fanfic", "Fanfiction erstellen", "AI Fanfiction", "Fanfic Generator deutsch", "Kostenloser Fanfic Generator", "AI Story Generator"],
	authors: [{ name: "AI Fanfic Generator Team" }],
	icons: {
		icon: [
			{ url: "/images/logo/favicon-16x16.png", sizes: "16x16", type: "image/png" },
			{ url: "/images/logo/favicon-32x32.png", sizes: "32x32", type: "image/png" },
			{ url: "/images/logo/android-chrome-192x192.png", sizes: "192x192", type: "image/png" },
			{ url: "/images/logo/android-chrome-512x512.png", sizes: "512x512", type: "image/png" },
		],
		apple: "/images/logo/apple-touch-icon.png",
		shortcut: "/images/logo/favicon.ico",
	},
	manifest: "/manifest.webmanifest",
	openGraph: {
		title: config.appName,
		description: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
		type: "website",
		locale: "en_US",
		siteName: config.appName,
		url: getBaseUrl(),
	},
	twitter: {
		card: "summary_large_image",
		title: config.appName,
		description: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
