import { GoogleGenAI } from "@google/genai";
import { type NextRequest, NextResponse } from "next/server";

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

export async function POST(request: NextRequest) {
	try {
		if (!GEMINI_API_KEY) {
			console.error("Gemini API key is not configured");
			return NextResponse.json(
				{ error: "AI service is not properly configured" },
				{ status: 500 },
			);
		}

		const body = await request.json();
		const { fandom, model = "flash" } = body;

		// Validate required fields
		if (!fandom || !fandom.trim()) {
			return NextResponse.json(
				{ error: "Fandom is required" },
				{ status: 400 },
			);
		}

		// Create the AI prompt for story idea generation
		const storyIdeaPrompt = `Act as a fanfiction story inspiration generator. Create a unique, specific, and conflict-driven fanfiction story idea for me. The story must revolve around the characters and plot of "${fandom.trim()}". Ensure the resulting story is both interesting and creative.

Keep it concise. The story idea should be like a movie trailer—short, powerful, and captivating.

Refer to the following examples to mimic their style and structure:

Example 1: The longship breeze is almost within <PERSON>'s grasp...if they're not thwarted by a bodyguard first.

Example 2: An injured person, someone with a gun, and someone with an inhumanly long lifespan walk into a coffeeshop...

Example 3: Kehinde must find a god cat before someone with one week left to live does.

Requirements:
1. Create a compelling hook that immediately draws the reader in
2. Include conflict or tension (something that creates obstacles)
3. Be specific to the ${fandom.trim()} universe and its characters
4. Keep it to 1-2 sentences maximum
5. End with suspense or intrigue
6. Use vivid, engaging language

Write only the story idea without any additional commentary, formatting, or explanations.`;

		// Initialize Google GenAI
		const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

		// Determine which model to use
		const modelName =
			model === "pro" ? "gemini-2.5-pro" : "gemini-2.5-flash";

		// Call Gemini API
		const response = await ai.models.generateContent({
			model: modelName,
			contents: [
				{
					role: "user",
					parts: [{ text: storyIdeaPrompt }],
				},
			],
		});

		// Extract text from response more safely
		let storyIdea = "";
		try {
			// Try the direct text property first (if available)
			if (response.text) {
				storyIdea = response.text.trim();
			}
			// Fallback to candidates structure
			else if (response.candidates?.[0]?.content?.parts?.[0]?.text) {
				storyIdea = response.candidates[0].content.parts[0].text.trim();
			}
		} catch (extractError) {
			console.error("Error extracting text from response:", extractError);
			console.log(
				"Response structure:",
				JSON.stringify(response, null, 2),
			);
		}

		// Validate that we got some content
		if (!storyIdea) {
			console.error(
				"No text content found in response:",
				JSON.stringify(response, null, 2),
			);
			return NextResponse.json(
				{ error: "No story idea generated. Please try again." },
				{ status: 500 },
			);
		}

		// Clean up the response (remove any unwanted formatting)
		storyIdea = storyIdea
			.replace(/^["']|["']$/g, "") // Remove quotes at start/end
			.replace(/\n\s*\n/g, " ") // Replace multiple newlines with space
			.trim();

		return NextResponse.json({
			storyIdea,
			fandom: fandom.trim(),
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Error generating story idea:", error);

		// Provide more specific error messages
		let errorMessage = "Error generating story idea";

		if (error instanceof Error) {
			if (error.message.includes("API key")) {
				errorMessage =
					"API key is not configured. Please contact the administrator.";
			} else if (error.message.includes("fetch failed")) {
				errorMessage =
					"Network error. Please check your internet connection and try again.";
			} else if (error.message.includes("quota")) {
				errorMessage = "API limit reached. Please try again later.";
			} else if (error.message.includes("safety")) {
				errorMessage =
					"Content filtered for safety. Please try a different fandom or approach.";
			} else {
				errorMessage = `Error: ${error.message}`;
			}
		}

		return NextResponse.json({ error: errorMessage }, { status: 500 });
	}
}
