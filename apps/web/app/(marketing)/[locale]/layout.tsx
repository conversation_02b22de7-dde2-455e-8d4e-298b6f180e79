import { Footer } from "@marketing/shared/components/Footer";
import { NavBar } from "@marketing/shared/components/NavBar";
import { config } from "@repo/config";
import { getMessagesForLocale } from "@repo/i18n";
import { getBaseUrl } from "@repo/utils";
import { SessionProvider } from "@saas/auth/components/SessionProvider";
import { Document } from "@shared/components/Document";
import { I18nProvider as FumadocsI18nProvider } from "fumadocs-ui/i18n";
import { RootProvider as FumadocsRootProvider } from "fumadocs-ui/provider";
import type { Metadata } from "next";
import { NextIntlClientProvider } from "next-intl";
import { getMessages, setRequestLocale } from "next-intl/server";
import { notFound } from "next/navigation";
import type { PropsWithChildren } from "react";

const locales = Object.keys(config.i18n.locales);

export function generateStaticParams() {
	return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>;
}): Promise<Metadata> {
	const { locale } = await params;
	const messages = await getMessagesForLocale(locale);
	const appName = messages.common?.appName || config.appName;

	// Get localized descriptions
	const descriptions = {
		en: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
		de: "Erstellen Sie fantastische Fanfiction-Geschichten mit unserem AI Fanfic Generator. Kostenloses Tool für alle Fandoms - schreiben Sie überzeugende Fan Fiction in Minuten mit KI-Unterstützung.",
	};

	const keywords = {
		en: [
			"AI Fanfic Generator",
			"Fanfiction Generator",
			"AI Fanfiction",
			"Fan Fiction Writer",
			"Story Generator",
			"Creative Writing AI",
			"Fanfic Creator",
		],
		de: [
			"AI Fanfic Generator",
			"Fanfiction Generator",
			"KI Fanfiction",
			"Fan Fiction Schreiber",
			"Geschichte Generator",
			"Kreatives Schreiben KI",
			"Fanfic Ersteller",
		],
	};

	const description =
		descriptions[locale as keyof typeof descriptions] || descriptions.en;
	const keywordList =
		keywords[locale as keyof typeof keywords] || keywords.en;

	return {
		title: {
			absolute: appName,
			default: appName,
			template: `%s | ${appName}`,
		},
		description,
		keywords: keywordList,
		authors: [{ name: `${appName} Team` }],
		alternates: {
			canonical: getBaseUrl(),
		},
		icons: {
			icon: [
				{
					url: "/images/logo/favicon-16x16.png",
					sizes: "16x16",
					type: "image/png",
				},
				{
					url: "/images/logo/favicon-32x32.png",
					sizes: "32x32",
					type: "image/png",
				},
				{
					url: "/images/logo/android-chrome-192x192.png",
					sizes: "192x192",
					type: "image/png",
				},
				{
					url: "/images/logo/android-chrome-512x512.png",
					sizes: "512x512",
					type: "image/png",
				},
			],
			apple: "/images/logo/apple-touch-icon.png",
			shortcut: "/images/logo/favicon.ico",
		},
		manifest: "/manifest.webmanifest",
		openGraph: {
			title: appName,
			description,
			type: "website",
			locale: locale === "de" ? "de_DE" : "en_US",
			siteName: appName,
			url: getBaseUrl(),
		},
		twitter: {
			card: "summary_large_image",
			title: appName,
			description,
		},
	};
}

export default async function MarketingLayout({
	children,
	params,
}: PropsWithChildren<{ params: Promise<{ locale: string }> }>) {
	const { locale } = await params;

	setRequestLocale(locale);

	if (!locales.includes(locale as any)) {
		notFound();
	}

	const messages = await getMessages();

	return (
		<Document locale={locale}>
			<FumadocsI18nProvider locale={locale}>
				<FumadocsRootProvider
					search={{
						enabled: true,
						options: {
							api: "/api/docs-search",
						},
					}}
				>
					<NextIntlClientProvider locale={locale} messages={messages}>
						<SessionProvider>
							<NavBar />
							<main className="min-h-screen">{children}</main>
							<Footer />
						</SessionProvider>
					</NextIntlClientProvider>
				</FumadocsRootProvider>
			</FumadocsI18nProvider>
		</Document>
	);
}
