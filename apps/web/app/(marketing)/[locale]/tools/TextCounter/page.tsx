import { SpeechTimerTool } from "@marketing/tools/components/SpeechTimerTool";
import { LocaleLink } from "@i18n/routing";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { CheckIcon, ClockIcon, SettingsIcon, ZapIcon, GiftIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import type { Metadata } from "next";
import Script from "next/script";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>;
}): Promise<Metadata> {
	const { locale } = await params;
	const t = await getTranslations("textCounter");

	return {
		title: t("title"),
		description: t("description"),
		keywords: locale === "de"
			? ["Text Zähler", "Wörter zählen", "Zeichen zählen", "Text Analyzer", "Schreibtools"]
			: ["Text Counter", "Word Count", "Character Count", "Text Analyzer", "Writing Tools"],
		openGraph: {
			title: t("title"),
			description: t("description"),
			type: "website",
		},
	};
}

export default async function TextCounterPage() {
	const t = await getTranslations("textCounter");

	// Structured data for SEO
	const structuredData = {
		"@context": "https://schema.org",
		"@type": "WebApplication",
		"name": t("title"),
		"description": t("description"),
		"applicationCategory": "UtilityApplication",
		"operatingSystem": "Web Browser",
		"offers": {
			"@type": "Offer",
			"price": "0",
			"priceCurrency": "USD"
		},
		"featureList": [
			t("features.accurate.title"),
			t("features.customizable.title"),
			t("features.free.title"),
			t("features.instant.title")
		]
	};

	return (
		<>
			<Script
				id="structured-data"
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(structuredData),
				}}
			/>
			<div className="container mx-auto px-4 py-8 pt-32 max-w-6xl">
			{/* Header */}
			<div className="text-center mb-12">
				<h1 className="text-4xl font-bold mb-4">{t("title")}</h1>
				<p className="text-xl text-muted-foreground max-w-3xl mx-auto">
					{t("description")}
				</p>
			</div>

			{/* Main Tool */}
			<div className="mb-16">
				<SpeechTimerTool />
			</div>

			{/* Features Section */}
			<div className="mb-16">
				<h2 className="text-3xl font-bold text-center mb-8">{t("features.title")}</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					<Card>
						<CardHeader className="text-center">
							<ClockIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
							<CardTitle className="text-lg">{t("features.accurate.title")}</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription className="text-center">
								{t("features.accurate.description")}
							</CardDescription>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="text-center">
							<SettingsIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
							<CardTitle className="text-lg">{t("features.customizable.title")}</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription className="text-center">
								{t("features.customizable.description")}
							</CardDescription>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="text-center">
							<GiftIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
							<CardTitle className="text-lg">{t("features.free.title")}</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription className="text-center">
								{t("features.free.description")}
							</CardDescription>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="text-center">
							<ZapIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
							<CardTitle className="text-lg">{t("features.instant.title")}</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription className="text-center">
								{t("features.instant.description")}
							</CardDescription>
						</CardContent>
					</Card>
				</div>
			</div>

			{/* CTA Section */}
			<div className="text-center">
				<Card className="max-w-2xl mx-auto">
					<CardHeader>
						<CardTitle className="text-2xl">{t("cta.title")}</CardTitle>
						<CardDescription className="text-lg">
							{t("cta.description")}
						</CardDescription>
					</CardHeader>
					<CardContent className="text-center">
						<LocaleLink href="/#trauerrede-form" className="inline-block">
							<Button size="lg" className="text-lg px-8 py-3">
								{t("cta.button")}
							</Button>
						</LocaleLink>
					</CardContent>
				</Card>
			</div>
		</div>
		</>
	);
}
