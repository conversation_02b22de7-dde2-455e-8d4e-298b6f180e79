import { LocaleLink } from "@i18n/routing";
import { StoryIdeaGeneratorTool } from "@marketing/tools/components/StoryIdeaGeneratorTool";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { GiftIcon, LightbulbIcon, SparklesIcon, ZapIcon } from "lucide-react";
import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import Script from "next/script";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>;
}): Promise<Metadata> {
	const { locale } = await params;
	const t = await getTranslations("storyIdeaGenerator");

	return {
		title: t("title"),
		description: t("description"),
		keywords:
			locale === "de"
				? [
						"Story Ideen Generator",
						"Fanfiction Ideen",
						"Kreatives Schreiben",
						"Story Generator",
						"Fanfic Inspiration",
					]
				: [
						"Story Idea Generator",
						"Fanfiction Ideas",
						"Creative Writing",
						"Story Generator",
						"Fanfic Inspiration",
					],
		openGraph: {
			title: t("title"),
			description: t("description"),
			type: "website",
		},
	};
}

export default async function StoryIdeaGeneratorPage() {
	const t = await getTranslations("storyIdeaGenerator");

	// Structured data for SEO
	const structuredData = {
		"@context": "https://schema.org",
		"@type": "WebApplication",
		name: t("title"),
		description: t("description"),
		applicationCategory: "UtilityApplication",
		operatingSystem: "Web Browser",
		offers: {
			"@type": "Offer",
			price: "0",
			priceCurrency: "USD",
		},
		featureList: [
			t("features.creative.title"),
			t("features.instant.title"),
			t("features.free.title"),
			t("features.unlimited.title"),
		],
	};

	return (
		<>
			<Script
				id="structured-data"
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(structuredData),
				}}
			/>
			<div className="container mx-auto px-4 py-8 pt-32 max-w-6xl">
				{/* Header */}
				<div className="text-center mb-12">
					<h1 className="text-4xl font-bold mb-4">{t("title")}</h1>
					<p className="text-xl text-muted-foreground max-w-3xl mx-auto">
						{t("description")}
					</p>
				</div>

				{/* Main Tool */}
				<div className="mb-16">
					<StoryIdeaGeneratorTool />
				</div>

				{/* Features Section */}
				<div className="mb-16">
					<h2 className="text-3xl font-bold text-center mb-8">
						{t("features.title")}
					</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						<Card>
							<CardHeader className="text-center">
								<LightbulbIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
								<CardTitle className="text-lg">
									{t("features.creative.title")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<CardDescription className="text-center">
									{t("features.creative.description")}
								</CardDescription>
							</CardContent>
						</Card>

						<Card>
							<CardHeader className="text-center">
								<ZapIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
								<CardTitle className="text-lg">
									{t("features.instant.title")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<CardDescription className="text-center">
									{t("features.instant.description")}
								</CardDescription>
							</CardContent>
						</Card>

						<Card>
							<CardHeader className="text-center">
								<GiftIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
								<CardTitle className="text-lg">
									{t("features.free.title")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<CardDescription className="text-center">
									{t("features.free.description")}
								</CardDescription>
							</CardContent>
						</Card>

						<Card>
							<CardHeader className="text-center">
								<SparklesIcon className="w-12 h-12 mx-auto mb-4 text-primary" />
								<CardTitle className="text-lg">
									{t("features.unlimited.title")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<CardDescription className="text-center">
									{t("features.unlimited.description")}
								</CardDescription>
							</CardContent>
						</Card>
					</div>
				</div>

				{/* How It Works Section */}
				<div className="mb-16">
					<h2 className="text-3xl font-bold text-center mb-8">
						{t("howItWorks.title")}
					</h2>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
						<div className="text-center">
							<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
								<span className="text-2xl font-bold text-primary">
									1
								</span>
							</div>
							<h3 className="text-xl font-semibold mb-2">
								{t("howItWorks.step1.title")}
							</h3>
							<p className="text-muted-foreground">
								{t("howItWorks.step1.description")}
							</p>
						</div>
						<div className="text-center">
							<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
								<span className="text-2xl font-bold text-primary">
									2
								</span>
							</div>
							<h3 className="text-xl font-semibold mb-2">
								{t("howItWorks.step2.title")}
							</h3>
							<p className="text-muted-foreground">
								{t("howItWorks.step2.description")}
							</p>
						</div>
						<div className="text-center">
							<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
								<span className="text-2xl font-bold text-primary">
									3
								</span>
							</div>
							<h3 className="text-xl font-semibold mb-2">
								{t("howItWorks.step3.title")}
							</h3>
							<p className="text-muted-foreground">
								{t("howItWorks.step3.description")}
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<Card className="max-w-2xl mx-auto">
						<CardHeader>
							<CardTitle className="text-2xl">
								{t("cta.title")}
							</CardTitle>
							<CardDescription className="text-lg">
								{t("cta.description")}
							</CardDescription>
						</CardHeader>
						<CardContent className="text-center">
							<LocaleLink
								href="/#fanfic-generator-form"
								className="inline-block"
							>
								<Button size="lg" className="text-lg px-8 py-3">
									{t("cta.button")}
								</Button>
							</LocaleLink>
						</CardContent>
					</Card>
				</div>
			</div>
		</>
	);
}
