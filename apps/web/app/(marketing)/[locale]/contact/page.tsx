import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { CheckCircle, Mail, Shield, Clock, Globe } from "lucide-react";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();
	return {
		title: t("contact.title"),
		description: t("contact.description"),
	};
}

export default async function ContactPage() {
	const t = await getTranslations();
	return (
		<div className="container max-w-6xl pt-32 pb-16">
			{/* Header */}
			<div className="mb-16 text-center">
				<h1 className="mb-4 font-bold text-5xl">
					{t("contact.title")}
				</h1>
				<p className="text-balance text-lg text-muted-foreground max-w-2xl mx-auto">
					{t("contact.description")}
				</p>
			</div>

			<div className="grid gap-8 lg:grid-cols-3">
				{/* About Section */}
				<div className="lg:col-span-2">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<CheckCircle className="h-5 w-5 text-primary" />
								{t("contact.about.title")}
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<p className="text-muted-foreground">
								{t("contact.about.description")}
							</p>
							<div>
								<h4 className="font-semibold mb-3">{t("contact.about.features.title")}</h4>
								<ul className="space-y-2">
									<li className="flex items-start gap-2">
										<CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
										<span className="text-sm">{t("contact.about.features.personalized")}</span>
									</li>
									<li className="flex items-start gap-2">
										<CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
										<span className="text-sm">{t("contact.about.features.professional")}</span>
									</li>
									<li className="flex items-start gap-2">
										<CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
										<span className="text-sm">{t("contact.about.features.quick")}</span>
									</li>
									<li className="flex items-start gap-2">
										<CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
										<span className="text-sm">{t("contact.about.features.editable")}</span>
									</li>
									<li className="flex items-start gap-2">
										<CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
										<span className="text-sm">{t("contact.about.features.private")}</span>
									</li>
								</ul>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="h-5 w-5 text-primary" />
								{t("contact.privacy.title")}
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<p className="text-muted-foreground">
								{t("contact.privacy.description")}
							</p>
							<ul className="space-y-2">
								<li className="flex items-start gap-2">
									<Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<span className="text-sm">{t("contact.privacy.points.noStorage")}</span>
								</li>
								<li className="flex items-start gap-2">
									<Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<span className="text-sm">{t("contact.privacy.points.encryption")}</span>
								</li>
								<li className="flex items-start gap-2">
									<Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<span className="text-sm">{t("contact.privacy.points.noSharing")}</span>
								</li>
								<li className="flex items-start gap-2">
									<Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<span className="text-sm">{t("contact.privacy.points.gdpr")}</span>
								</li>
								<li className="flex items-start gap-2">
									<Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<span className="text-sm">{t("contact.privacy.points.deletion")}</span>
								</li>
							</ul>
						</CardContent>
					</Card>
				</div>

				{/* Contact Info */}
				<div>
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Mail className="h-5 w-5 text-primary" />
								{t("contact.contact.title")}
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<p className="text-muted-foreground">
								{t("contact.contact.description")}
							</p>
							<div className="space-y-3">
								<div className="flex items-center gap-2">
									<Mail className="h-4 w-4 text-primary" />
									<span className="text-sm">{t("contact.contact.email")}</span>
								</div>
								<div className="flex items-center gap-2">
									<Globe className="h-4 w-4 text-primary" />
									<span className="text-sm">{t("contact.contact.website")}</span>
								</div>
								<div className="flex items-center gap-2">
									<Clock className="h-4 w-4 text-primary" />
									<span className="text-sm">{t("contact.contact.response")}</span>
								</div>
								<div className="flex items-center gap-2">
									<Clock className="h-4 w-4 text-blue-600" />
									<span className="text-sm">{t("contact.contact.hours")}</span>
								</div>
								<div className="flex items-center gap-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span className="text-sm">{t("contact.contact.support")}</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
