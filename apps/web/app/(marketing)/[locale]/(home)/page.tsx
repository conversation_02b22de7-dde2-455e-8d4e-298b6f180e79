import { FaqSection } from "@marketing/home/<USER>/FaqSection";
import { FeaturesSection } from "@marketing/home/<USER>/FeaturesSection";
import { <PERSON> } from "@marketing/home/<USER>/Hero";
import { HowItWorks } from "@marketing/home/<USER>/HowItWorks";
import { PricingSection } from "@marketing/home/<USER>/PricingSection";
import { FanficGeneratorForm } from "@marketing/home/<USER>/FanficGeneratorForm";

import { getBaseUrl } from "@repo/utils";
import type { Metadata } from "next";
import { setRequestLocale } from "next-intl/server";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>;
}): Promise<Metadata> {
	const { locale } = await params;

	const titles = {
		en: "Create Amazing Fan Fiction Stories",
		de: " Erstelle fantastische Fan Fiction Geschichten",
	};

	const descriptions = {
		en: "Create amazing fanfiction stories with our AI Fanfic Generator. Free tool for all fandoms - write compelling fan fiction in minutes with AI assistance.",
		de: "Erstellen Sie fantastische Fanfiction-Geschichten mit unserem AI Fanfic Generator. Kostenloses Tool für alle Fandoms - schreiben Sie überzeugende Fan Fiction in Minuten mit KI-Unterstützung.",
	};

	const title = titles[locale as keyof typeof titles] || titles.en;
	const description = descriptions[locale as keyof typeof descriptions] || descriptions.en;

	return {
		title,
		description,
		alternates: {
			canonical: getBaseUrl(),
		},
		openGraph: {
			title,
			description,
			type: "website",
			locale: locale === "de" ? "de_DE" : "en_US",
			url: getBaseUrl(),
		},
		twitter: {
			card: "summary_large_image",
			title,
			description,
		},
	};
}

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<>
			<Hero />
			<div id="fanfic-generator-form" className="scroll-mt-16 py-16 lg:py-24">
				<div className="container">
					<FanficGeneratorForm />
				</div>
			</div>
			<HowItWorks />
			<PricingSection />
			<FeaturesSection />
			<FaqSection />
		</>
	);
}
