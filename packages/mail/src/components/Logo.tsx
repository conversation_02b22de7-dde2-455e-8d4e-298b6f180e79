import React from "react";

export function Logo({
	withLabel = true,
	appName = "KI Trauerrede", // Default to German, can be overridden
}: {
	withLabel?: boolean;
	appName?: string;
}) {
	return (
		<span className="flex items-center font-semibold text-primary leading-none">
			<img
				src="/images/logo/android-chrome-192x192.png"
				alt={`${appName} Logo`}
				className="h-12 w-12"
			/>
			{withLabel && <span className="ml-3 text-xl">{appName}</span>}
		</span>
	);
}
