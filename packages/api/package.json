{"dependencies": {"@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.8.2", "@sindresorhus/slugify": "^2.2.1", "hono": "^4.7.7", "hono-openapi": "^0.4.6", "nanoid": "^5.1.5", "openai": "^4.95.0", "openapi-merge": "^1.3.3", "use-intl": "^4.0.2", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.1.2", "encoding": "^0.1.13", "prisma": "^6.6.0", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}