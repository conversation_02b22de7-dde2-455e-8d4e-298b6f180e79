---
description: 
globs: 
alwaysApply: true
---
This rule describes the key principles of how to generate code for this project.

Whenever code is generated assume you are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI and Tailwind.

- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.

Follow Next.js docs for Data Fetching, Rendering, and Routing.
Follow the documentation at supastarter.dev/docs/nextjs for supastarter specifc patterns.
